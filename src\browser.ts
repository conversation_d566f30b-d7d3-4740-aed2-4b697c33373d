import { Buffer } from 'buffer'
import { detectImageFormat, img2icoJs } from './pure.js'
import { DEFAULT_SIZES, IcoOptions, IcoBuffer } from './types.js'

// window.Buffer = Buffer

let wasm: { img2ico: (buffer: Uint8Array, sizes: Uint32Array) => Uint8Array; } | null = null;

// Asynchronously load the WASM module for the Web
(async () => {
  try {
    const wasmModule = await import('./wasm-web/img2ico_wasm.js')
    await wasmModule.default() // This is often required for web-targeted wasm-pack output
    wasm = wasmModule
  } catch (e) {
    console.log('Failed to load WASM module, falling back to pure JS implementation.', e)
    wasm = null
  }
})()

/**
 * Converts a source image Buffer into a .ico format Buffer.
 * Automatically uses WASM if available, otherwise falls back to a pure JS implementation.
 */
export default async function img2ico(
  buffer: ArrayBuffer | Buffer,
  options: IcoOptions = {}
): Promise<IcoBuffer> {
  buffer = Buffer.isBuffer(buffer) ? buffer : Buffer.from(buffer)
  const sizes = options.sizes || DEFAULT_SIZES

  const supportedFormats = ['png', 'jpeg', 'bmp', 'webp']
  const detectedFormat = detectImageFormat(buffer)

  if (!detectedFormat || !supportedFormats.includes(detectedFormat)) {
    throw new Error(`Unsupported image format: ${detectedFormat || 'unknown'}. Only PNG, JPEG, BMP, and WebP are supported.`)
  }

  if (wasm) {
    try {
      const icoUint8Array = wasm.img2ico(buffer, new Uint32Array(sizes))
      const icoBuffer = Buffer.from(icoUint8Array)
      return new IcoBuffer(icoBuffer)
    } catch (error) {
      console.error('WASM execution failed, falling back to JS implementation.', error)
    }
  }

  return await img2icoJs(Buffer.from(buffer), options)
}

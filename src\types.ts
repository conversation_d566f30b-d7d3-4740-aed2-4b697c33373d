import { Buffer } from 'buffer'

export const DEFAULT_SIZES = [16, 24, 32, 48, 64, 96, 128, 256]

/**
 * Options for ICO generation.
 */
export interface IcoOptions {
  /**
   * An array of icon sizes (in pixels) to be generated.
   * The source image will be resized to each of these dimensions.
   */
  sizes?: number[];
}

/**
 * Represents the result of an ICO conversion, providing methods to access the ICO data.
 * This class extends Buffer, allowing direct use as a Buffer while providing additional methods.
 */
export class IcoBuffer extends Buffer {
  constructor(buffer: Buffer) {
    super(buffer)
  }

  /**
   * Returns the ICO data as a Base64 encoded Data URL.
   */
  toDataUrl(): string {
    return `data:image/x-icon;base64,${this.toString('base64')}`;
  }
}
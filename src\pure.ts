import { <PERSON><PERSON>, ResizeStrategy } from 'jimp'
import { <PERSON><PERSON><PERSON> } from 'buffer'
import { DEFAULT_SIZES, IcoOptions, IcoBuffer } from './types.js'

export function detectImageFormat(buffer: Buffer): string | null {
  // PNG: 89 50 4E 47 0D 0A 1A 0A
  if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47 &&
    buffer[4] === 0x0D && buffer[5] === 0x0A && buffer[6] === 0x1A && buffer[7] === 0x0A) {
    return 'png'
  }
  // JPEG: FF D8 FF
  if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
    return 'jpeg'
  }
  // BMP: 42 4D
  if (buffer[0] === 0x42 && buffer[1] === 0x4D) {
    return 'bmp'
  }
  // WebP: RIFFxxxxWEBP (52 49 46 46 ... 57 45 42 50)
  if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46 &&
    buffer[8] === 0x57 && buffer[9] === 0x45 && buffer[10] === 0x42 && buffer[11] === 0x50) {
    return 'webp'
  }
  return null
}

/**
 * Converts a source image Buffer into a .ico format Buffer using pure JS.
 *
 * @param {Buffer} buffer The source image data as a Buffer.
 * @param {IcoOptions} [options={}] Configuration options for ICO generation.
 * @returns {Promise<Buffer>} A Promise that resolves with a Buffer containing the .ico file data.
 */
export async function img2icoJs(
  buffer: ArrayBuffer | Buffer,
  options: IcoOptions = {}
): Promise<IcoBuffer> {

  const sizes = options.sizes || DEFAULT_SIZES

  const baseImage = await Jimp.fromBuffer(buffer)

  const pngBuffers = await Promise.all(
    sizes.map(async (size) => {
      const canvas = new Jimp({ width: size, height: size, color: 0x0 }) // 0x0 is transparent
      const scaledImage = baseImage.clone().scaleToFit({ w: size, h: size, mode: ResizeStrategy.HERMITE })
      const x = (size - scaledImage.width) / 2
      const y = (size - scaledImage.height) / 2
      canvas.composite(scaledImage, x, y)
      return canvas.getBuffer('image/png')
    })
  )

  const headerSize = 6
  const directoryEntrySize = 16
  const headerAndDirectorySize = headerSize + pngBuffers.length * directoryEntrySize
  const headerBuffer = Buffer.alloc(headerAndDirectorySize)

  headerBuffer.writeUInt16LE(0, 0)
  headerBuffer.writeUInt16LE(1, 2)
  headerBuffer.writeUInt16LE(pngBuffers.length, 4)

  let currentOffset = headerAndDirectorySize

  for (let i = 0; i < pngBuffers.length; i++) {
    const pngBuffer = pngBuffers[i]
    const size = sizes[i]
    const width = size >= 256 ? 0 : size
    const height = size >= 256 ? 0 : size
    const imageSizeInBytes = pngBuffer.length
    const entryOffset = headerSize + i * directoryEntrySize

    headerBuffer.writeUInt8(width, entryOffset)
    headerBuffer.writeUInt8(height, entryOffset + 1)
    headerBuffer.writeUInt8(0, entryOffset + 2)
    headerBuffer.writeUInt8(0, entryOffset + 3)
    headerBuffer.writeUInt16LE(0, entryOffset + 4)
    headerBuffer.writeUInt16LE(0, entryOffset + 6)
    headerBuffer.writeUInt32LE(imageSizeInBytes, entryOffset + 8)
    headerBuffer.writeUInt32LE(currentOffset, entryOffset + 12)

    currentOffset += imageSizeInBytes
  }

  const icoBuffer = Buffer.concat([headerBuffer, ...pngBuffers])

  return new IcoBuffer(icoBuffer)
}
